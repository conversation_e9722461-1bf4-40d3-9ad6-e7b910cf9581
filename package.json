{"name": "yixiaoer-lite", "version": "4.6.2", "main": "./out/main/index.js", "author": "长沙草儿绽放科技有限公司", "homepage": "https://yixiaoer.cn", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "staging": "electron-vite dev --mode staging", "prod": "electron-vite dev --mode production", "win-dev": "chcp 65001 && electron-vite dev", "postinstall": "electron-builder install-app-deps", "build-renderer:production": "npm run typecheck && electron-vite build --mode production", "build-app:production:unpack": "npm run build-renderer:production && electron-builder --config electron-builder.production.js --dir", "build-app:production:win-ia32": "npm run build-renderer:production && electron-builder --config electron-builder.production.js --win --ia32", "build-app:production:win-x64": "npm run build-renderer:production && electron-builder --config electron-builder.production.js --win --x64", "build-app:production:mac-arm64": "npm run build-renderer:production && electron-builder --config electron-builder.production.js --mac --arm64", "build-app:production:mac-x64": "npm run build-renderer:production && electron-builder --config electron-builder.production.js --mac --x64", "build-app:production:linux": "npm run build-renderer:production && electron-builder --config electron-builder.production.js --linux", "build-renderer:staging": "npm run typecheck && electron-vite build --mode staging", "build-app:staging:unpack": "npm run build-renderer:staging && electron-builder --config electron-builder.staging.js --dir", "build-app:staging:win-ia32": "npm run build-renderer:staging && electron-builder --config electron-builder.staging.js --win --ia32", "build-app:staging:win-x64": "npm run build-renderer:staging && electron-builder --config electron-builder.staging.js --win --x64", "build-app:staging:mac-arm64": "npm run build-renderer:staging && electron-builder --config electron-builder.staging.js --mac --arm64", "build-app:staging:mac-x64": "npm run build-renderer:staging && electron-builder --config electron-builder.staging.js --mac --x64", "build-app:staging:linux": "npm run build-renderer:staging && electron-builder --config electron-builder.staging.js --linux", "prepare": "husky", "clean": "npx rimraf ./dist ./out ./node_modules"}, "dependencies": {"@coozf/editor": "2.0.0-beta.13", "@electron-toolkit/preload": "3.0.1", "@electron-toolkit/utils": "3.0.0", "@hookform/resolvers": "3.9.0", "@mdi/font": "7.4.47", "@radix-ui/react-alert-dialog": "1.1.1", "@radix-ui/react-aspect-ratio": "1.1.0", "@radix-ui/react-avatar": "1.1.0", "@radix-ui/react-checkbox": "1.1.1", "@radix-ui/react-collapsible": "1.1.1", "@radix-ui/react-context-menu": "2.2.2", "@radix-ui/react-dialog": "1.1.1", "@radix-ui/react-dropdown-menu": "2.1.1", "@radix-ui/react-hover-card": "1.1.1", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-menubar": "1.1.6", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-progress": "1.1.0", "@radix-ui/react-radio-group": "1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "1.1.0", "@radix-ui/react-slider": "1.2.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-switch": "1.1.1", "@radix-ui/react-tabs": "1.1.0", "@radix-ui/react-toast": "1.2.1", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-toolbar": "1.1.0", "@radix-ui/react-tooltip": "1.1.2", "@radix-ui/react-visually-hidden": "1.1.0", "@react-spring/web": "9.7.5", "@tailwindcss/typography": "0.5.15", "@tanstack/react-query": "5.51.21", "@tanstack/react-table": "8.20.1", "@tiptap-pro/extension-emoji": "2.13.0", "@tiptap-pro/extension-file-handler": "2.13.0", "@tiptap/extension-bubble-menu": "2.11.7", "@tiptap/extension-character-count": "2.11.5", "@tiptap/extension-color": "2.9.1", "@tiptap/extension-font-family": "2.10.3", "@tiptap/extension-hard-break": "2.11.5", "@tiptap/extension-image": "2.9.1", "@tiptap/extension-link": "2.9.1", "@tiptap/extension-placeholder": "2.9.1", "@tiptap/extension-text-align": "2.11.5", "@tiptap/pm": "2.9.1", "@tiptap/react": "2.9.1", "@tiptap/starter-kit": "2.9.1", "@types/crypto-js": "4.2.2", "@types/lodash-es": "4.17.12", "@types/react": "18.3.3", "@types/react-dom": "18.3.1", "@types/uuid": "9.0.8", "@vitejs/plugin-react": "4.3.1", "adm-zip": "0.5.16", "apexcharts": "4.7.0", "axios": "1.7.2", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "cmdk": "1.0.0", "crypto-js": "4.2.0", "date-fns": "3.6.0", "dexie": "4.0.7", "dexie-export-import": "4.1.2", "electron-log": "5.1.5", "electron-store": "10.0.1", "electron-updater": "6.2.1", "embla-carousel-autoplay": "8.5.2", "embla-carousel-react": "8.5.2", "eslint-plugin-react": "7.35.0", "framer-motion": "11.11.17", "fs-extra": "11.2.0", "he": "1.2.0", "iconv-lite": "0.6.3", "image-size": "1.1.1", "immer": "10.1.1", "input-otp": "1.4.1", "lodash-es": "4.17.21", "lucide-react": "0.419.0", "mammoth": "1.9.0", "mediainfo.js": "0.3.1", "mime": "3.0.0", "nanoid": "5.1.5", "next-themes": "0.4.3", "node-downloader-helper": "2.1.9", "node-machine-id": "1.1.12", "p-limit": "6.2.0", "pako": "2.1.0", "qrcode.react": "4.2.0", "react-apexcharts": "1.7.0", "react-day-picker": "8.10.1", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dropzone": "14.2.10", "react-easy-crop": "5.1.0", "react-hook-form": "7.52.2", "react-image-crop": "11.0.7", "react-intersection-observer": "9.14.0", "react-is": "18.3.1", "react-photo-view": "1.2.6", "react-player": "2.16.0", "react-resizable-panels": "2.1.6", "react-router-dom": "6.26.0", "react-virtuoso": "4.12.0", "react-zoom-pan-pinch": "3.6.1", "reflect-metadata": "0.2.2", "sharp": "0.33.5", "socket.io-client": "4.7.5", "sonner": "1.7.0", "swiper": "11.1.14", "tailwind-merge": "2.4.0", "tailwindcss-animate": "1.0.7", "use-effect-event": "1.0.2", "use-immer": "0.10.0", "uuid": "9.0.1", "vaul": "1.1.1", "zod": "3.23.8", "zustand": "4.5.4"}, "devDependencies": {"@electron-toolkit/eslint-config": "1.0.2", "@electron-toolkit/eslint-config-prettier": "2.0.0", "@electron-toolkit/eslint-config-ts": "2.0.0", "@electron-toolkit/tsconfig": "1.0.1", "@electron/notarize": "2.3.2", "@rushstack/eslint-patch": "1.10.3", "@types/adm-zip": "0.5.5", "@types/mime": "3.0.0", "@types/node": "20.14.8", "@types/pako": "2.0.3", "@volcengine/tos-sdk": "2.7.4", "@yixiaoer/platform-service": "2.11.11", "ali-oss": "6.20.0", "autoprefixer": "10.4.19", "chokidar": "4.0.1", "co": "4.6.0", "cross-env": "7.0.3", "dotenv": "16.4.5", "electron": "31.3.1", "electron-builder": "24.13.3", "electron-vite": "2.3.0", "eslint": "8.57.0", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-unused-imports": "4.1.3", "husky": "9.0.11", "jsdom": "24.1.0", "less": "4.2.0", "less-loader": "12.2.0", "postcss": "8.4.40", "prettier": "3.3.2", "prettier-plugin-tailwindcss": "0.6.5", "react": "18.3.1", "react-dom": "18.3.1", "sass": "1.77.5", "tailwindcss": "3.4.7", "typescript": "5.5.4", "vite": "5.2.13", "vite-plugin-svgr": "4.2.0", "vitest": "1.6.0"}, "volta": {"node": "22.11.0"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild", "sharp"]}}