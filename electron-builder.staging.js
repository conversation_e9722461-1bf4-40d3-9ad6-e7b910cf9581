require('dotenv').config({
  path: './.env.staging',
})

console.log('process.env.NODE_ENV', process.env.NODE_ENV)
console.log('process.env.VITE_APP_APPID', process.env.VITE_APP_APPID)
console.log('process.env.VITE_APP_NAME', process.env.VITE_APP_NAME)
console.log('process.env.VITE_API_URL', process.env.VITE_API_URL)

let artifactName = '${name}-${version}-${os}-${arch}-测试环境.${ext}'

const publishUrl = `https://lite-download.yixiaoer.cn/staging/director`

// 默认取当前系统的架构
let channel_arch = process.arch
// 如果是windows系统，且指定了ia32，则使用ia32架构
if (process.argv.some((x) => x === '--win')) {
  if (process.argv.some((x) => x === '--ia32')) {
    channel_arch = 'ia32'
  }
} else if (process.argv.some((x) => x === '--mac')) {
  if (process.argv.some((x) => x === '--arm64')) {
    channel_arch = 'arm64'
  }
}

module.exports = {
  appId: process.env.VITE_APP_APPID,
  productName: process.env.VITE_APP_NAME,
  copyright: 'Copyright © 2023 长沙草儿绽放科技有限公司',
  compression: 'normal',
  artifactName: artifactName,
  afterSign: 'build/notarize.js',
  generateUpdatesFilesForAllChannels: true,
  directories: {
    buildResources: 'build',
  },
  publish: [
    {
      provider: 'generic',
      channel: `${process.platform}-${channel_arch}`,
      url: publishUrl,
    },
  ],
  files: [
    '!**/.vscode/*',
    '!src/*',
    '!electron.vite.config.{js,ts,mjs,cjs}',
    '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}',
    '!{.env,.env.*,.npmrc,pnpm-lock.yaml}',
    '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}',
  ],
  asarUnpack:
    channel_arch === 'arm64'
      ? ['resources/**', '**/node_modules/sharp/**/*']
      : ['resources/**', '**/node_modules/sharp/**/*', '**/node_modules/@img/**/*'],
  win: {
    executableName: 'yixiaoer-lite',
    target: {
      target: 'nsis',
    },
    publisherName: null, // 此处高能预警，请勿调整，必须为null；由于 electron-update@4.3.4 升级后增加了发行者验证，将出现安装包下载后因验证失败而被自动删除。
    verifyUpdateCodeSignature: false, // 验证更新签名
    signingHashAlgorithms: ['sha256'],
    certificateFile: 'build/yixiaoer.pfx',
    certificatePassword: process.env.CERTIFICATE_PASSWORD,
  },
  nsis: {
    oneClick: false, // 一键安装
    perMachine: true, // 始终为本机安装
    allowElevation: true, // 允许请求提升。 如果为false，则用户必须使用提升的权限重新启动安装程序。
    allowToChangeInstallationDirectory: true, // 允许修改安装目录
    deleteAppDataOnUninstall: true, // 卸载时删除应用数据
    createDesktopShortcut: true, // 创建桌面图标
    createStartMenuShortcut: true, // 创建开始菜单图标
    license: 'LICENCE.rtf', // 用户协议文件
  },
  mac: {
    target: 'default',
    hardenedRuntime: true,
    entitlements: './build/entitlements.mac.plist',
    entitlementsInherit: 'build/entitlements.mac.plist',
    extendInfo: {
      NSCameraUsageDescription: "Application requests access to the device's camera.",
      NSMicrophoneUsageDescription: "Application requests access to the device's microphone.",
      NSDocumentsFolderUsageDescription:
        "Application requests access to the user's Documents folder.",
      NSDownloadsFolderUsageDescription:
        "Application requests access to the user's Downloads folder.",
    },
    notarize: false,
  },
  dmg: {
    contents: [
      {
        x: 410,
        y: 150,
        type: 'link',
        path: '/Applications',
      },
      {
        x: 130,
        y: 150,
      },
    ],
  },
  npmRebuild: false,
  electronDownload: {
    mirror: 'https://npmmirror.com/mirrors/electron/',
  },
}
