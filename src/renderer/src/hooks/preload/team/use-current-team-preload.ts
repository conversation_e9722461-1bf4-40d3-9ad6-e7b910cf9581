import { useCallback, useEffect } from 'react'
import { useContextStore } from '@renderer/store/contextStore'
import { useTeamService } from '@renderer/infrastructure/services'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authorizeEvents, teamEvents } from '@renderer/infrastructure/event-bus/business-events'
import { useQuery } from '@tanstack/react-query'
import { useCurrentTeamId } from '../use-current-team-id'

export function useCurrentTeamPreload() {
  const teamService = useTeamService()
  const { setCurrentTeam, currentTeam: storeCurrentTeam } = useContextStore(
    (state) => ({
      setCurrentTeam: state.setCurrentTeam,
      currentTeam: state.currentTeam,
    }
  ))

  const currentTeamId = useCurrentTeamId()

  const { data: currentTeam } = useQuery({
    queryKey: ['getTeamDetail', currentTeamId],
    queryFn: () => teamService.getTeamDetail(currentTeamId),
    initialData: null,
  })

  useEffect(() => {
    setCurrentTeam(currentTeam)

    return () => {
      setCurrentTeam(null)
    }
  }, [currentTeam, setCurrentTeam])

  useEffect(() => {
    return eventBus.on(teamEvents.teamDetailChanged, async (teamId: string) => {
      if (teamId !== currentTeamId) return
      const teamDetail = await teamService.getTeamDetail(teamId)
      setCurrentTeam(teamDetail)
    })
  }, [currentTeamId, setCurrentTeam, teamService])

  const updateCurrentTeamDetail = useCallback(async () => {
    setCurrentTeam(await teamService.getTeamDetail(currentTeamId))
  }, [currentTeamId, setCurrentTeam, teamService])

  // 更新账号用量
  useEffect(() => {
    return eventBus.on(authorizeEvents.accountAdded, updateCurrentTeamDetail)
  }, [updateCurrentTeamDetail])

  useEffect(() => {
    return eventBus.on(authorizeEvents.accountDeleted, updateCurrentTeamDetail)
  }, [updateCurrentTeamDetail])

  // 更新成员用量
  useEffect(() => {
    return eventBus.on(teamEvents.memberInvited, updateCurrentTeamDetail)
  }, [updateCurrentTeamDetail])

  useEffect(() => {
    return eventBus.on(teamEvents.memberRemoved, updateCurrentTeamDetail)
  }, [updateCurrentTeamDetail])

  useEffect(() => {
    return eventBus.on(teamEvents.thirdSpaceTokenChanged, updateCurrentTeamDetail)
  }, [updateCurrentTeamDetail])

  return {
    ready: !!storeCurrentTeam,
  }
}
