import { Button } from '@renderer/shadcn-components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@renderer/shadcn-components/ui/popover'
import { cn } from '@renderer/lib/utils'
import { CalendarIcon } from 'lucide-react'
import type { HTMLAttributes } from 'react'
import { useState, useMemo } from 'react'
import type { DateRange as InnerDateRange } from 'react-day-picker'
import { Calendar } from '@renderer/shadcn-components/ui/calendar'
import { datetimeService } from '@renderer/infrastructure/services'
import { zhCN } from 'date-fns/locale'
import { DateRange } from '@renderer/infrastructure/model/utils/date-range'
import CancelIcon from '@renderer/assets/common/cancel.svg?react'
import { subDays, isAfter, isBefore, startOfDay, endOfDay } from 'date-fns'

export function DateRangePicker({
  className,
  value,
  onChange,
  disabled = false,
}: {
  value: DateRange | null
  onChange: (value: DateRange | null) => void
  className?: HTMLAttributes<HTMLDivElement>['className']
  disabled?: boolean
}) {
  const [date, setDate] = useState<InnerDateRange | undefined>(
    value
      ? {
          from: value.from,
          to: value.to,
        }
      : undefined,
  )

  // 计算可选择的日期范围：当天往前7天
  const dateRange = useMemo(() => {
    const today = new Date()
    const sevenDaysAgo = subDays(today, 7)
    return {
      min: startOfDay(sevenDaysAgo),
      max: endOfDay(today),
    }
  }, [])

  // 禁用不在范围内的日期
  const isDateDisabled = useMemo(() => {
    return (date: Date) => {
      return isBefore(date, dateRange.min) || isAfter(date, dateRange.max)
    }
  }, [dateRange])

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          id="date"
          variant={'outline'}
          className={cn(
            'justify-start bg-white pl-2 text-left font-normal',
            !date && 'text-muted-foreground',
            disabled && 'cursor-not-allowed',
          )}
          disabled={disabled}
        >
          <CalendarIcon color={'#706F72'} size={18} />
          {date?.from ? (
            date.to ? (
              <>
                {datetimeService.formatToDate(date.from)} - {datetimeService.formatToDate(date.to)}
              </>
            ) : (
              <span>{datetimeService.formatToDate(date.from)} - 请选择结束日期</span>
            )
          ) : (
            <span>请选择作品发布日期</span>
          )}
          {date && (
            <div
              onClick={(e) => {
                e.stopPropagation()
                setDate(undefined)
                onChange(null)
              }}
            >
              <CancelIcon className="h-3 w-3"></CancelIcon>
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          locale={zhCN}
          initialFocus
          mode="range"
          defaultMonth={date?.from || dateRange.max}
          selected={date}
          onSelect={(range) => {
            setDate(range)
            if (range && range.from && range.to) {
              onChange(new DateRange(range.from, range.to))
            }
          }}
          numberOfMonths={2}
          disabled={isDateDisabled}
        />
      </PopoverContent>
    </Popover>
  )
}
