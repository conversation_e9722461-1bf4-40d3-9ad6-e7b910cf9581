import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import { Input } from '@renderer/shadcn-components/ui/input'
import { Switch } from '@renderer/shadcn-components/ui/switch'
import SuperIcon from '@renderer/assets/team/super.png'
import MarketAgentIcon from '@renderer/assets/team/marketAgent.png'
import { useEffect, useState } from 'react'
import { useTeamService } from '@renderer/infrastructure/services'
import { useQuery } from '@tanstack/react-query'
import { useCurrentTeam } from '@renderer/hooks/use-current-team'
import { toast } from 'sonner'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@renderer/shadcn-components/ui/sheet'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import help1 from '@renderer/assets/super/1.png'
import help2 from '@renderer/assets/super/2.png'
import help3 from '@renderer/assets/super/3.png'

import marketHelp1 from '@renderer/assets/marketAgent/1.png'
import marketHelp2 from '@renderer/assets/marketAgent/2.png'
import marketHelp3 from '@renderer/assets/marketAgent/3.png'
import { useInnerContextStore } from '@renderer/store/contextStore'

export function TeamPlugins() {
  const team = useCurrentTeam()
  const teamService = useTeamService()

  const [open, setOpen] = useState(false)
  const [marketAgentOpen, setMarketAgentOpen] = useState(false)
  const [superdir, setSuperdir] = useState<{
    id: string
    name: string
    enabled: boolean
    componentArgs: Record<string, unknown>
  }>({
    componentArgs: { token: '' },
    enabled: false,
    name: '',
    id: '',
  })

  const [marketAgent, setMarketAgent] = useState<{
    id: string
    name: string
    enabled: boolean
    componentArgs: Record<string, unknown>
  }>({
    componentArgs: { token: '' },
    enabled: false,
    name: '',
    id: '',
  })

  const { data: currentTeam } = useQuery({
    queryKey: ['getTeamDetail-plugins'],
    queryFn: () => {
      return teamService.getTeamDetail(team.id)
    },
    initialData: null,
  })

  useEffect(() => {
    const superdir = currentTeam?.components?.find((c) => c.name === 'superdir')
    setSuperdir(superdir ?? { componentArgs: { token: '' }, enabled: false, id: '', name: '' })

    const marketAgent = currentTeam?.components?.find((c) => c.name === 'marketAgent')
    setMarketAgent(
      marketAgent ?? { componentArgs: { token: '' }, enabled: false, id: '', name: '' },
    )
  }, [currentTeam])

  return (
    <div className="grid grid-cols-2 gap-4">
      <div className="flex flex-col gap-3 rounded-lg border border-[#E4E6EB] p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 font-semibold">
            <img src={SuperIcon} alt="" className="h-[24px]" />
            超级编导
          </div>
          <Switch
            checked={superdir.enabled}
            onCheckedChange={(check) => {
              teamService.setTeamPlugin(team.id, {
                name: 'superdir',
                componentArgs: {
                  token: superdir.componentArgs.token,
                },
                enable: check,
              })

              useInnerContextStore.getState().setCurrentTeam({
                ...team,
                components: (team.components || []).map((c) => {
                  if (c.name === 'superdir') {
                    return {
                      ...c,
                      enabled: check,
                    }
                  }
                  return c
                }),
              })

              setSuperdir({
                ...superdir,
                enabled: check,
              })
            }}
          />
        </div>
        <div className="text-sm">
          超级编导是围绕短视频生态打造的集创意脚本、批量剪辑合成、视频分发为一体的创意生产平台。
        </div>
        <div className="flex gap-2">
          <Dialog onOpenChange={setOpen} open={open}>
            <DialogTrigger asChild>
              <Button>设置</Button>
            </DialogTrigger>
            <DialogContent className="w-[480px] bg-white">
              <DialogHeader>
                <DialogTitle>设置「超级编导」</DialogTitle>
                <DialogDescription></DialogDescription>
              </DialogHeader>
              <Input
                value={superdir.componentArgs.token as string}
                placeholder="请输入帐号token"
                onChange={(e) => {
                  setSuperdir({
                    ...superdir,
                    componentArgs: {
                      ...superdir.componentArgs,
                      token: e.target.value,
                    },
                  })
                }}
              />
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">取消</Button>
                </DialogClose>
                <Button
                  onClick={async () => {
                    const enable = superdir.enabled
                    await teamService.setTeamPlugin(team.id, {
                      name: 'superdir',
                      componentArgs: {
                        token: superdir.componentArgs.token,
                      },
                      enable: enable,
                    })

                    setSuperdir({
                      ...superdir,
                      enabled: enable,
                    })

                    useInnerContextStore.getState().setCurrentTeam({
                      ...team,
                      components: (team.components || []).map((c) => {
                        if (c.name === 'superdir') {
                          return {
                            ...c,
                            componentArgs: {
                              ...c.componentArgs,
                              token: superdir.componentArgs.token,
                            },
                          }
                        }
                        return c
                      }),
                    })
                    toast.success('保存成功')
                    setOpen(false)
                  }}
                >
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline">如何使用?</Button>
            </SheetTrigger>
            <SheetContent className="flex w-[560px] !max-w-none flex-col overflow-hidden bg-white">
              <SheetHeader>
                <SheetTitle>如何绑定「超级编导」帐号?</SheetTitle>
              </SheetHeader>
              <ScrollArea className="w-full flex-1">
                <div className="flex flex-col gap-2">
                  <img src={help3} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    <span>{'1.打开<超级编导>链接下载 '}</span>
                    <span
                      className="cursor-pointer text-[#4F46E5]"
                      onClick={() => {
                        window.open('https://member.superdir.cn/yixiaoer')
                      }}
                    >
                      https://member.superdir.cn/yixiaoer
                    </span>
                  </div>
                  <img src={help2} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    {'2. 注册并在【团队概况】-【团队资料信息】【复制token】'}
                  </div>
                  <img src={help1} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    {'3. 绑定【超级编导】账号 粘贴token 完成绑定操作'}
                  </div>
                </div>
              </ScrollArea>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      <div className="flex flex-col gap-3 rounded-lg border border-[#E4E6EB] p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 font-semibold">
            <img src={MarketAgentIcon} alt="" className="h-[24px]" />
            AI营销智能体
          </div>
          <Switch
            checked={marketAgent.enabled}
            onCheckedChange={(check) => {
              teamService.setTeamPlugin(team.id, {
                name: 'marketAgent',
                componentArgs: {
                  token: marketAgent.componentArgs.token,
                },
                enable: check,
              })

              useInnerContextStore.getState().setCurrentTeam({
                ...team,
                components: (team.components || []).map((c) => {
                  if (c.name === 'marketAgent') {
                    return {
                      ...c,
                      enabled: check,
                    }
                  }
                  return c
                }),
              })

              setMarketAgent({
                ...marketAgent,
                enabled: check,
              })
            }}
          />
        </div>
        <div className="text-sm">
          AI营销智能体是一套企业级AI数字化转型引流获客＋私域成交的应用平台，专注于通过AI技术为企业提供一站式营销获客解决方案
        </div>
        <div className="flex gap-2">
          <Dialog onOpenChange={setMarketAgentOpen} open={marketAgentOpen}>
            <DialogTrigger asChild>
              <Button>设置</Button>
            </DialogTrigger>
            <DialogContent className="w-[480px] bg-white">
              <DialogHeader>
                <DialogTitle>设置「AI营销智能体」</DialogTitle>
                <DialogDescription></DialogDescription>
              </DialogHeader>
              <Input
                value={marketAgent.componentArgs.token as string}
                placeholder="请输入帐号token"
                onChange={(e) => {
                  setMarketAgent({
                    ...marketAgent,
                    componentArgs: {
                      ...marketAgent.componentArgs,
                      token: e.target.value,
                    },
                  })
                }}
              />
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">取消</Button>
                </DialogClose>
                <Button
                  onClick={async () => {
                    const enable = marketAgent.enabled
                    await teamService.setTeamPlugin(team.id, {
                      name: 'marketAgent',
                      componentArgs: {
                        token: marketAgent.componentArgs.token,
                      },
                      enable: enable,
                    })

                    setMarketAgent({
                      ...marketAgent,
                      enabled: enable,
                    })

                    useInnerContextStore.getState().setCurrentTeam({
                      ...team,
                      components: (team.components || []).map((c) => {
                        if (c.name === 'marketAgent') {
                          return {
                            ...c,
                            componentArgs: {
                              ...c.componentArgs,
                              token: marketAgent.componentArgs.token,
                            },
                          }
                        }
                        return c
                      }),
                    })
                    toast.success('保存成功')
                    setMarketAgentOpen(false)
                  }}
                >
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline">如何使用?</Button>
            </SheetTrigger>
            <SheetContent className="flex w-[560px] !max-w-none flex-col overflow-hidden bg-white">
              <SheetHeader>
                <SheetTitle>如何获取平台token?</SheetTitle>
              </SheetHeader>
              <ScrollArea className="w-full flex-1">
                <div className="flex flex-col gap-2">
                  <img src={marketHelp1} alt={'aboutImg'} />
                  <div className="flex items-center justify-center text-sm">
                    <span>{'1.点击右上角【用户信息】'}</span>
                    <span
                      className="cursor-pointer text-[#4F46E5]"
                      onClick={() => {
                        window.open('https://webv2.hxjpayment.cn/#/usercenter')
                      }}
                    >
                      https://webv2.hxjpayment.cn/#/usercenter
                    </span>
                  </div>
                  <img src={marketHelp2} alt={'aboutImg'} />
                  <div className="flex items-center justify-center text-sm">
                    {'2. 点击【密钥管理】'}
                  </div>
                  <img src={marketHelp3} alt={'aboutImg'} />
                  <div className="flex items-center justify-center text-sm">
                    {'3. 跳转至AI营销智能体token，点击【复制】'}
                  </div>
                </div>
              </ScrollArea>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  )
}
