import { useScenarioContext } from '@renderer/context/scenario-context'
import { FormItem as PublishFormItem } from './FormItem'
import { AccountsSelector } from '@renderer/pages/Publish/components/AccountsSelector'
import type {
  Account,
  ImageFileInfo,
  Platform,
  SingleVideoAccountViewModel,
  SpiderAccount,
  VideoFileInfo,
} from '@renderer/infrastructure/model'
import { VideoSelector } from '@renderer/pages/Publish/components/VideoSelector'
import CoverSelector from '@renderer/pages/Publish/components/cover-selector'
import { useEffect, useMemo, useRef } from 'react'
import { useValidation } from '@renderer/hooks/validation/validation'
import { ValidationMessage } from '@renderer/components/ValidationMessage'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { Validator } from '@renderer/infrastructure/validation/validator'
import {
  useCoverValidator,
  useVideoValidator,
} from '@renderer/pages/Publish/validation/useVideoContentValidation'
import { getVideoSpecification } from '@renderer/pages/Publish/specification/content-type/video/video-specification'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import {
  BalanceSummaryContext,
  CommonSummaryContext,
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import { VideoAdder } from './VideoAdder'
import type { DraftFunction } from 'use-immer'
import { videoPlatforms } from '@renderer/pages/Publish/specification/content-type/supports'
import {
  SessionCheckProgress,
  SessionValidSummary,
} from '@renderer/pages/Publish/components/session-check/session-check-progress'
import { useWechat3rdPartyService } from '@renderer/infrastructure/services/application-service/account/wechat-3rd-party-service'
import { mediaService } from '@renderer/infrastructure/services'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { useTrafficValidator } from '../validation/useBalanceValidation'
import { platformNames } from '@common/model/platform-name'
import { SuperValidationMessage } from '@renderer/components/SuperValidationMessage'

const validators = {
  accounts: new Validator<Account[]>().addRule((subject) => {
    if (subject.length === 0) {
      return new RuleResult('invalid', '请添加至少一个账号')
    }
    return RuleResult.valid
  }),
  accountsValid: new Validator<Account[]>().addRule((subject) => {
    if (subject.some((account) => !account.isSessionValid())) {
      return new RuleResult('invalid', '失效账号无法发布')
    }
    return RuleResult.valid
  }),
  superVideo: new Validator<{ video: VideoFileInfo | null; account: Account[] }>().addRule(
    (subject) => {
      if (subject.video?.superId) {
        if (
          subject.account.filter((account) => account.platform.name === platformNames.DouYin)
            .length > 1
        ) {
          return new RuleResult('invalid', '超级编导视频素材一个视频只能对应一个抖音账号')
        }
      }
      return RuleResult.valid
    },
  ),
  video: new Validator<VideoFileInfo | null>().addRule((subject) => {
    if (!(subject && subject.filePath && subject.filePath.length > 0)) {
      return new RuleResult('invalid', '请选择视频')
    }
    return RuleResult.valid
  }),
  cover: new Validator<ImageFileInfo | null>().addRule((subject) => {
    if (!(subject && subject.path && subject.path.length > 0)) {
      return new RuleResult('invalid', '请选择封面')
    }
    return RuleResult.valid
  }),
}

interface SingleVideoAccountProps {
  config: SingleVideoAccountViewModel
  setConfig: (draft: DraftFunction<SingleVideoAccountViewModel>) => void
  selectedPlatforms: Platform[]
  onVideoAdd: (videos: VideoFileInfo[]) => void
}

export function SingleVideoAccount({
  config,
  setConfig,
  selectedPlatforms,
  onVideoAdd,
}: SingleVideoAccountProps) {
  const { setAccounts, accountsWithTokens } = useScenarioContext()
  const { updateAccountLocks } = useWechat3rdPartyService()

  const canAddVideo = config.accounts.length <= 1 || config.video === null

  const videoSelectMode = config.accounts.length > 1 ? 'single' : 'multiple'

  const formState = useFormStateContext(FormStateContext)

  const commonSummary = useSummaryContext(CommonSummaryContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)
  const balanceSummary = useSummaryContext(BalanceSummaryContext)

  const { conclusion: accountsConclusion } = useValidation(
    config.accounts,
    validators.accounts,
    commonSummary,
  )

  useValidation(config.accounts, validators.accountsValid, commonSummary)

  const { conclusion: superVideoConclusion } = useValidation(
    useMemo(
      () => ({ video: config.video, account: config.accounts }),
      [config.accounts, config.video],
    ),
    validators.superVideo,
    commonSummary,
  )

  // 将要使用的流量
  const trafficAboutToUse = useMemo(() => {
    const fileByteSize = config.video?.fileByteSize
    if (fileByteSize) {
      return fileByteSize.multiply(config.accounts.length)
    }
    return ByteSize.fromB(0)
  }, [config.accounts.length, config.video?.fileByteSize])

  useValidation(trafficAboutToUse, useTrafficValidator(), balanceSummary)

  const { conclusion: commonVideoConclusion } = useValidation(
    config.video,
    validators.video,
    commonSummary,
  )

  useValidation(config.video, useVideoValidator(selectedPlatforms), platformSummary)

  const { conclusion: commonCoverConclusion } = useValidation(
    config.cover,
    validators.cover,
    commonSummary,
  )

  useValidation(config.cover, useCoverValidator(selectedPlatforms), platformSummary)

  const lastVideo = useRef<VideoFileInfo | null>(config.video)

  useEffect(() => {
    void (async () => {
      if ((!config.cover || config.video !== lastVideo.current) && config.video) {
        lastVideo.current = config.video
        const coverFileInfo = await mediaService.captureVideo(config.video)
        if (coverFileInfo) {
          setConfig((draft) => {
            draft.cover = coverFileInfo
          })
        }
      }
    })()
  }, [config.cover, config.video, setConfig])

  return (
    <div className="flex flex-col space-y-9 pr-2">
      <PublishFormItem label="视频" required={true}>
        <div className="flex gap-2">
          {config.video && (
            <VideoSelector
              className="h-[180px] w-[135px]"
              video={config.video}
              onChange={(videoInfo: VideoFileInfo | null) => {
                formState.setDirty(true)
                setConfig((draft) => {
                  draft.video = videoInfo
                })
              }}
            />
          )}
          {canAddVideo && (
            <VideoAdder
              selectSuperIds={[config.video?.superId].filter(Boolean) as string[]}
              selectMediaIds={[config.video?.mediaId].filter(Boolean) as string[]}
              className="h-[180px] w-[135px]"
              onAdd={(videos: VideoFileInfo[]) => {
                formState.setDirty(true)
                onVideoAdd(videos)
              }}
              multiple={videoSelectMode === 'multiple'}
            />
          )}
        </div>
        <ValidationMessage
          subject={config.video}
          conclusion={commonVideoConclusion}
          formState={formState}
        />
      </PublishFormItem>

      <PublishFormItem label="账号" required={true} contentWidthLimited={false}>
        <div>
          <AccountsSelector
            selectedAccounts={config.accounts}
            onChange={async (accounts: Account[]) => {
              const newAccountsWithTokens = await updateAccountLocks(
                accounts as SpiderAccount[],
                accountsWithTokens,
              )
              setAccounts(newAccountsWithTokens)
              formState.setDirty(true)
              setConfig((draft) => {
                draft.accounts = accounts as SpiderAccount[] //这里目前选出来的必然是SpiderAccount
              })
            }}
            selectable={(account: Account) =>
              getVideoSpecification(account.platform).contentTypeSupport
            }
            platforms={videoPlatforms}
          />
          <ValidationMessage
            subject={config.accounts}
            conclusion={accountsConclusion}
            formState={formState}
          />
          <SuperValidationMessage
            subject={{ video: config.video, account: config.accounts }}
            conclusion={superVideoConclusion}
            formState={formState}
          />
        </div>

        <SessionCheckProgress />
        <SessionValidSummary accounts={config.accounts} />
      </PublishFormItem>

      <PublishFormItem label="封面" required={true}>
        <CoverSelector
          video={config.video}
          cover={config.cover}
          onChange={(cover: ImageFileInfo | null) => {
            formState.setDirty(true)
            setConfig((draft) => {
              draft.cover = cover
            })
          }}
        />
        <ValidationMessage
          subject={config.cover}
          conclusion={commonCoverConclusion}
          formState={formState}
        />
      </PublishFormItem>
    </div>
  )
}
